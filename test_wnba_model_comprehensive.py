#!/usr/bin/env python3
"""
Comprehensive WNBA Model Testing
================================

Test the trained WNBA model with various scenarios and analyze its performance.
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Any
import json
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("WNBA_MODEL_TEST")

class ProvenNeuralNetwork(nn.Module):
    """Neural network architecture proven to work with audited features"""
    
    def __init__(self, input_dim: int, hidden_dim: int = 32, dropout_rate: float = 0.7):
        super(ProvenNeuralNetwork, self).__init__()
        
        self.network = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.BatchNorm1d(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            
            nn.Linear(hidden_dim // 2, 2)
        )
        
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                nn.init.zeros_(module.bias)
    
    def forward(self, x):
        return self.network(x)
    
    def forward_inference(self, x):
        """Forward pass for inference - handles single samples"""
        x = self.network[0](x)  # Linear
        
        if x.size(0) == 1:
            bn1 = self.network[1]
            x = (x - bn1.running_mean) / torch.sqrt(bn1.running_var + bn1.eps)
            x = x * bn1.weight + bn1.bias
        else:
            x = self.network[1](x)
            
        x = self.network[2](x)  # ReLU
        x = self.network[3](x)  # Dropout
        x = self.network[4](x)  # Linear
        
        if x.size(0) == 1:
            bn2 = self.network[5]
            x = (x - bn2.running_mean) / torch.sqrt(bn2.running_var + bn2.eps)
            x = x * bn2.weight + bn2.bias
        else:
            x = self.network[5](x)
            
        x = self.network[6](x)  # ReLU
        x = self.network[7](x)  # Dropout
        x = self.network[8](x)  # Final Linear
        
        return x

class WNBAModelTester:
    """Comprehensive WNBA model testing suite"""
    
    def __init__(self):
        self.model = None
        self.load_model()
        
        # WNBA teams with realistic strength ratings
        self.team_strengths = {
            "Las Vegas Aces": 0.82,      # Defending champions
            "New York Liberty": 0.78,     # Strong contender
            "Connecticut Sun": 0.75,     # Consistent playoff team
            "Seattle Storm": 0.72,       # Veteran leadership
            "Minnesota Lynx": 0.68,      # Solid team
            "Indiana Fever": 0.65,       # Caitlin Clark effect
            "Chicago Sky": 0.62,         # Rebuilding
            "Atlanta Dream": 0.60,       # Young team
            "Phoenix Mercury": 0.58,     # Veteran stars
            "Dallas Wings": 0.55,        # Developing
            "Washington Mystics": 0.53,  # Rebuilding
            "Los Angeles Sparks": 0.50   # Struggling
        }
    
    def load_model(self):
        """Load the trained WNBA model"""
        try:
            model_path = "best_full_wnba_model.pth"
            self.model = ProvenNeuralNetwork(input_dim=9, hidden_dim=18, dropout_rate=0.7)
            self.model.load_state_dict(torch.load(model_path, map_location='cpu'))
            self.model.eval()
            logger.info("✅ WNBA model loaded successfully!")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to load model: {e}")
            return False
    
    def create_game_features(self, home_team: str, away_team: str) -> np.ndarray:
        """Create feature vector for game prediction"""
        home_strength = self.team_strengths.get(home_team, 0.60)
        away_strength = self.team_strengths.get(away_team, 0.60)
        
        # Create features matching training data structure
        stat_value = home_strength * 100
        season_encoded = 2024
        high_performer = 1 if home_strength > 0.70 else 0
        top_10_rank = 1 if home_strength > 0.65 else 0
        above_average_performer = 1 if home_strength > 0.60 else 0
        stat_value_normalized = (stat_value - 50) / 20
        stat_value_log = np.log(stat_value + 1)
        stat_value_squared = stat_value ** 2
        stat_value_percentile = home_strength * 100
        
        return np.array([
            stat_value, season_encoded, high_performer, top_10_rank,
            above_average_performer, stat_value_normalized, stat_value_log,
            stat_value_squared, stat_value_percentile
        ])
    
    def predict_game(self, home_team: str, away_team: str) -> Dict[str, float]:
        """Predict game outcome"""
        if self.model is None:
            return {'prediction': 0.5, 'confidence': 0.5}
        
        try:
            features = self.create_game_features(home_team, away_team)
            features_tensor = torch.FloatTensor(features).unsqueeze(0)
            
            with torch.no_grad():
                outputs = self.model.forward_inference(features_tensor)
                probabilities = torch.softmax(outputs, dim=1)
                home_win_prob = probabilities[0][1].item()
                confidence = max(probabilities[0]).item()
            
            return {
                'home_win_probability': home_win_prob,
                'away_win_probability': 1 - home_win_prob,
                'confidence': confidence,
                'home_team': home_team,
                'away_team': away_team
            }
            
        except Exception as e:
            logger.error(f"Prediction error: {e}")
            return {'prediction': 0.5, 'confidence': 0.5}
    
    def test_playoff_scenarios(self) -> List[Dict[str, Any]]:
        """Test model on realistic playoff scenarios"""
        playoff_matchups = [
            ("Las Vegas Aces", "New York Liberty"),      # Championship contenders
            ("Connecticut Sun", "Seattle Storm"),        # Veteran teams
            ("Minnesota Lynx", "Indiana Fever"),         # Mid-tier matchup
            ("Chicago Sky", "Atlanta Dream"),            # Rebuilding teams
            ("Phoenix Mercury", "Dallas Wings"),         # Lower tier
            ("Washington Mystics", "Los Angeles Sparks") # Bottom teams
        ]
        
        results = []
        logger.info("🏀 Testing Playoff Scenarios")
        logger.info("=" * 50)
        
        for home, away in playoff_matchups:
            prediction = self.predict_game(home, away)
            
            # Determine predicted winner
            if prediction['home_win_probability'] > 0.5:
                winner = home
                win_prob = prediction['home_win_probability']
            else:
                winner = away
                win_prob = prediction['away_win_probability']
            
            result = {
                'matchup': f"{away} @ {home}",
                'predicted_winner': winner,
                'win_probability': win_prob,
                'confidence': prediction['confidence'],
                'home_team_strength': self.team_strengths.get(home, 0.6),
                'away_team_strength': self.team_strengths.get(away, 0.6)
            }
            
            results.append(result)
            
            logger.info(f"   {away} @ {home}")
            logger.info(f"   → Winner: {winner} ({win_prob:.1%}, Conf: {prediction['confidence']:.1%})")
            logger.info("")
        
        return results
    
    def test_upset_detection(self) -> List[Dict[str, Any]]:
        """Test model's ability to detect potential upsets"""
        upset_scenarios = [
            ("Los Angeles Sparks", "Las Vegas Aces"),    # Weak vs Strong
            ("Washington Mystics", "New York Liberty"),  # Bottom vs Top
            ("Dallas Wings", "Connecticut Sun"),         # Developing vs Veteran
            ("Atlanta Dream", "Seattle Storm"),          # Young vs Experienced
        ]
        
        results = []
        logger.info("🎯 Testing Upset Detection")
        logger.info("=" * 50)
        
        for home, away in upset_scenarios:
            prediction = self.predict_game(home, away)
            
            home_strength = self.team_strengths.get(home, 0.6)
            away_strength = self.team_strengths.get(away, 0.6)
            strength_diff = abs(home_strength - away_strength)
            
            # Check if model predicts upset (weaker team wins)
            home_favored = home_strength > away_strength
            model_picks_home = prediction['home_win_probability'] > 0.5
            
            upset_predicted = (home_favored and not model_picks_home) or (not home_favored and model_picks_home)
            
            result = {
                'matchup': f"{away} @ {home}",
                'strength_difference': strength_diff,
                'upset_predicted': upset_predicted,
                'home_win_prob': prediction['home_win_probability'],
                'confidence': prediction['confidence']
            }
            
            results.append(result)
            
            upset_indicator = "🚨 UPSET!" if upset_predicted else "✅ Favorite"
            logger.info(f"   {away} @ {home} - {upset_indicator}")
            logger.info(f"   → Home: {prediction['home_win_probability']:.1%}, Conf: {prediction['confidence']:.1%}")
            logger.info("")
        
        return results
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run comprehensive model testing"""
        logger.info("🏀 COMPREHENSIVE WNBA MODEL TESTING")
        logger.info("🔥 Testing trained model on realistic scenarios")
        logger.info("=" * 60)
        
        # Test different scenarios
        playoff_results = self.test_playoff_scenarios()
        upset_results = self.test_upset_detection()
        
        # Calculate summary statistics
        all_confidences = [r['confidence'] for r in playoff_results + upset_results]
        avg_confidence = np.mean(all_confidences)
        
        upsets_predicted = sum(1 for r in upset_results if r['upset_predicted'])
        
        summary = {
            'model_loaded': self.model is not None,
            'total_predictions': len(playoff_results) + len(upset_results),
            'average_confidence': avg_confidence,
            'upsets_predicted': upsets_predicted,
            'playoff_scenarios': playoff_results,
            'upset_scenarios': upset_results,
            'test_timestamp': datetime.now().isoformat()
        }
        
        logger.info("=" * 60)
        logger.info("📊 COMPREHENSIVE TEST SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Model Status: {'✅ Loaded' if self.model else '❌ Failed'}")
        logger.info(f"Total Predictions: {summary['total_predictions']}")
        logger.info(f"Average Confidence: {avg_confidence:.1%}")
        logger.info(f"Upsets Predicted: {upsets_predicted}/{len(upset_results)}")
        logger.info("=" * 60)
        
        return summary

def main():
    """Main testing function"""
    tester = WNBAModelTester()
    results = tester.run_comprehensive_test()
    
    # Save results
    results_file = f"wnba_comprehensive_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to: {results_file}")
    return results

if __name__ == "__main__":
    main()
