2025-07-05 07:55:01,994 - INFO - STARTING FULL WNBA DATASET TRAINING
2025-07-05 07:55:01,994 - INFO - Using proven feature audit approach that achieved 90.1% accuracy
2025-07-05 07:55:01,994 - INFO - ================================================================================
2025-07-05 07:55:02,581 - INFO -  NBA API Endpoints loaded successfully (class import fix).
2025-07-05 07:55:02,619 - INFO - LOADING COMPLETE WNBA DATASET
2025-07-05 07:55:02,620 - INFO - Using database source for maximum data consistency
2025-07-05 07:55:02,622 - INFO - --- Initializing HYPER MEDUSA NEURAL VAULT Data Connector ---
2025-07-05 07:55:02,623 - INFO - BasketballDataLoader initialized with NBA API connector.
2025-07-05 07:55:02,870 - INFO -    games (schedule): 135,816 records
2025-07-05 07:55:02,870 - INFO -    player_game_stats (performance): 26,213 records
2025-07-05 07:55:03,029 - INFO - Successfully loaded complete WNBA dataset: (15417, 23)
2025-07-05 07:55:03,029 - INFO - Seasons covered: Unknown
2025-07-05 07:55:03,030 - INFO - Players covered: Unknown
2025-07-05 07:55:03,030 - INFO - APPLYING PROVEN FEATURE AUDIT
2025-07-05 07:55:03,030 - INFO - APPLYING PROVEN FEATURE AUDIT APPROACH
2025-07-05 07:55:03,030 - INFO - ============================================================
2025-07-05 07:55:03,030 - INFO - Original data: (15417, 23)
2025-07-05 07:55:03,036 - INFO - STEP 2: Removing 6 categorical features: ['rank_position', 'engineered_feature_15', 'engineered_feature_16', 'engineered_feature_17', 'engineered_feature_18']...
2025-07-05 07:55:03,039 - INFO - After categorical removal: (15417, 17)
2025-07-05 07:55:03,058 - INFO - STEP 4: Removing 7 low-variance features
2025-07-05 07:55:03,060 - INFO - ============================================================
2025-07-05 07:55:03,060 - INFO - FEATURE AUDIT COMPLETE: (15417, 23) -> (15417, 10)
2025-07-05 07:55:03,060 - INFO - Final features: ['stat_value', 'season_encoded', 'high_performer', 'top_10_rank', 'above_average_performer', 'stat_value_normalized', 'stat_value_log', 'stat_value_squared', 'stat_value_percentile', 'win_prediction']
2025-07-05 07:55:03,061 - INFO - ============================================================
2025-07-05 07:55:03,061 - INFO - PREPARING FEATURES AND TARGET
2025-07-05 07:55:03,068 - INFO - Target quality check: {np.int64(0): np.int64(7710), np.int64(1): np.int64(7707)}
2025-07-05 07:55:03,069 - INFO - Class balance ratio: 0.500
2025-07-05 07:55:03,079 - INFO - Train: 10791 samples, {np.int64(0): np.int64(5397), np.int64(1): np.int64(5394)}
2025-07-05 07:55:03,080 - INFO - Val: 2313 samples, {np.int64(0): np.int64(1156), np.int64(1): np.int64(1157)}
2025-07-05 07:55:03,080 - INFO - Test: 2313 samples, {np.int64(0): np.int64(1157), np.int64(1): np.int64(1156)}
2025-07-05 07:55:03,098 - INFO - CREATING PROVEN MODEL ARCHITECTURE
2025-07-05 07:55:03,119 - INFO - Model: 9 features -> 425 parameters
2025-07-05 07:55:03,120 - INFO - Feature-to-parameter ratio: 1:47
2025-07-05 07:55:05,092 - INFO - STARTING TRAINING ON COMPLETE DATASET
2025-07-05 07:55:05,092 - INFO - ============================================================
2025-07-05 07:55:05,166 - INFO - Epoch  1: Train Loss: 1.0262, Val Loss: 0.8659, Train Acc: 0.424, Val Acc: 0.122
2025-07-05 07:55:05,203 - INFO - Epoch  2: Train Loss: 1.0222, Val Loss: 0.8574, Train Acc: 0.426, Val Acc: 0.122
2025-07-05 07:55:05,224 - INFO - Epoch  3: Train Loss: 1.0127, Val Loss: 0.8508, Train Acc: 0.423, Val Acc: 0.122
2025-07-05 07:55:05,254 - INFO - Epoch  4: Train Loss: 1.0089, Val Loss: 0.8453, Train Acc: 0.425, Val Acc: 0.122
2025-07-05 07:55:05,273 - INFO - Epoch  5: Train Loss: 0.9713, Val Loss: 0.8390, Train Acc: 0.432, Val Acc: 0.122
2025-07-05 07:55:05,309 - INFO - Epoch  6: Train Loss: 0.9590, Val Loss: 0.8268, Train Acc: 0.445, Val Acc: 0.122
2025-07-05 07:55:05,321 - INFO - Epoch  7: Train Loss: 0.9497, Val Loss: 0.8154, Train Acc: 0.449, Val Acc: 0.092
2025-07-05 07:55:05,335 - INFO - Epoch  8: Train Loss: 0.9475, Val Loss: 0.8057, Train Acc: 0.448, Val Acc: 0.105
2025-07-05 07:55:05,359 - INFO - Epoch  9: Train Loss: 0.9350, Val Loss: 0.7961, Train Acc: 0.458, Val Acc: 0.123
2025-07-05 07:55:05,381 - INFO - Epoch 10: Train Loss: 0.9072, Val Loss: 0.7863, Train Acc: 0.455, Val Acc: 0.125
2025-07-05 07:55:05,398 - INFO - Epoch 11: Train Loss: 0.9056, Val Loss: 0.7774, Train Acc: 0.469, Val Acc: 0.127
2025-07-05 07:55:05,418 - INFO - Epoch 12: Train Loss: 0.9032, Val Loss: 0.7686, Train Acc: 0.474, Val Acc: 0.129
2025-07-05 07:55:05,437 - INFO - Epoch 13: Train Loss: 0.8920, Val Loss: 0.7604, Train Acc: 0.487, Val Acc: 0.132
2025-07-05 07:55:05,458 - INFO - Epoch 14: Train Loss: 0.8834, Val Loss: 0.7515, Train Acc: 0.482, Val Acc: 0.137
2025-07-05 07:55:05,477 - INFO - Epoch 15: Train Loss: 0.8731, Val Loss: 0.7431, Train Acc: 0.489, Val Acc: 0.529
2025-07-05 07:55:05,494 - INFO - Epoch 16: Train Loss: 0.8541, Val Loss: 0.7322, Train Acc: 0.493, Val Acc: 0.557
2025-07-05 07:55:05,512 - INFO - Epoch 17: Train Loss: 0.8569, Val Loss: 0.7213, Train Acc: 0.501, Val Acc: 0.568
2025-07-05 07:55:05,532 - INFO - Epoch 18: Train Loss: 0.8456, Val Loss: 0.7104, Train Acc: 0.499, Val Acc: 0.568
2025-07-05 07:55:05,559 - INFO - Epoch 19: Train Loss: 0.8353, Val Loss: 0.6998, Train Acc: 0.505, Val Acc: 0.568
2025-07-05 07:55:05,578 - INFO - Epoch 20: Train Loss: 0.8182, Val Loss: 0.6893, Train Acc: 0.509, Val Acc: 0.568
2025-07-05 07:55:05,605 - INFO - Epoch 21: Train Loss: 0.8269, Val Loss: 0.6795, Train Acc: 0.515, Val Acc: 0.568
2025-07-05 07:55:05,622 - INFO - Epoch 22: Train Loss: 0.8187, Val Loss: 0.6702, Train Acc: 0.515, Val Acc: 0.568
2025-07-05 07:55:05,659 - INFO - Epoch 23: Train Loss: 0.7973, Val Loss: 0.6613, Train Acc: 0.520, Val Acc: 0.568
2025-07-05 07:55:05,680 - INFO - Epoch 24: Train Loss: 0.8068, Val Loss: 0.6526, Train Acc: 0.524, Val Acc: 0.568
2025-07-05 07:55:05,703 - INFO - Epoch 25: Train Loss: 0.7858, Val Loss: 0.6443, Train Acc: 0.526, Val Acc: 0.568
2025-07-05 07:55:05,721 - INFO - Epoch 26: Train Loss: 0.7821, Val Loss: 0.6360, Train Acc: 0.542, Val Acc: 0.568
2025-07-05 07:55:05,744 - INFO - Epoch 27: Train Loss: 0.7665, Val Loss: 0.6273, Train Acc: 0.541, Val Acc: 0.568
2025-07-05 07:55:05,768 - INFO - Epoch 28: Train Loss: 0.7607, Val Loss: 0.6184, Train Acc: 0.552, Val Acc: 0.593
2025-07-05 07:55:05,806 - INFO - Epoch 29: Train Loss: 0.7710, Val Loss: 0.6100, Train Acc: 0.550, Val Acc: 0.706
2025-07-05 07:55:05,821 - INFO - Epoch 30: Train Loss: 0.7511, Val Loss: 0.6028, Train Acc: 0.563, Val Acc: 0.760
2025-07-05 07:55:05,841 - INFO - Epoch 31: Train Loss: 0.7512, Val Loss: 0.5968, Train Acc: 0.561, Val Acc: 0.792
2025-07-05 07:55:05,867 - INFO - Epoch 32: Train Loss: 0.7451, Val Loss: 0.5907, Train Acc: 0.560, Val Acc: 0.792
2025-07-05 07:55:05,889 - INFO - Epoch 33: Train Loss: 0.7253, Val Loss: 0.5850, Train Acc: 0.575, Val Acc: 0.822
2025-07-05 07:55:05,909 - INFO - Epoch 34: Train Loss: 0.7374, Val Loss: 0.5796, Train Acc: 0.569, Val Acc: 0.838
2025-07-05 07:55:05,927 - INFO - Epoch 35: Train Loss: 0.7115, Val Loss: 0.5744, Train Acc: 0.583, Val Acc: 0.843
2025-07-05 07:55:05,950 - INFO - Epoch 36: Train Loss: 0.7121, Val Loss: 0.5691, Train Acc: 0.583, Val Acc: 0.901
2025-07-05 07:55:05,977 - INFO - Epoch 37: Train Loss: 0.7078, Val Loss: 0.5642, Train Acc: 0.587, Val Acc: 0.905
2025-07-05 07:55:05,991 - INFO - Epoch 38: Train Loss: 0.7060, Val Loss: 0.5595, Train Acc: 0.592, Val Acc: 0.911
2025-07-05 07:55:06,014 - INFO - Epoch 39: Train Loss: 0.7023, Val Loss: 0.5549, Train Acc: 0.595, Val Acc: 0.920
2025-07-05 07:55:06,045 - INFO - Epoch 40: Train Loss: 0.7065, Val Loss: 0.5505, Train Acc: 0.598, Val Acc: 0.922
2025-07-05 07:55:06,081 - INFO - Epoch 41: Train Loss: 0.6901, Val Loss: 0.5461, Train Acc: 0.596, Val Acc: 0.925
2025-07-05 07:55:06,101 - INFO - Epoch 42: Train Loss: 0.6885, Val Loss: 0.5423, Train Acc: 0.604, Val Acc: 0.930
2025-07-05 07:55:06,122 - INFO - Epoch 43: Train Loss: 0.6758, Val Loss: 0.5386, Train Acc: 0.610, Val Acc: 0.930
2025-07-05 07:55:06,148 - INFO - Epoch 44: Train Loss: 0.6739, Val Loss: 0.5352, Train Acc: 0.612, Val Acc: 0.933
2025-07-05 07:55:06,179 - INFO - Epoch 45: Train Loss: 0.6797, Val Loss: 0.5318, Train Acc: 0.616, Val Acc: 0.933
2025-07-05 07:55:06,192 - INFO - Epoch 46: Train Loss: 0.6649, Val Loss: 0.5285, Train Acc: 0.626, Val Acc: 0.933
2025-07-05 07:55:06,210 - INFO - Epoch 47: Train Loss: 0.6801, Val Loss: 0.5251, Train Acc: 0.613, Val Acc: 0.936
2025-07-05 07:55:06,233 - INFO - Epoch 48: Train Loss: 0.6689, Val Loss: 0.5224, Train Acc: 0.620, Val Acc: 0.936
2025-07-05 07:55:06,252 - INFO - Epoch 49: Train Loss: 0.6788, Val Loss: 0.5197, Train Acc: 0.617, Val Acc: 0.936
2025-07-05 07:55:06,272 - INFO - Epoch 50: Train Loss: 0.6576, Val Loss: 0.5169, Train Acc: 0.630, Val Acc: 0.936
2025-07-05 07:55:06,275 - INFO - FINAL EVALUATION ON COMPLETE DATASET
2025-07-05 07:55:06,298 - INFO - ================================================================================
2025-07-05 07:55:06,298 - INFO - FULL DATASET TRAINING RESULTS
2025-07-05 07:55:06,298 - INFO - ================================================================================
2025-07-05 07:55:06,298 - INFO - Final Test Accuracy: 0.9473
2025-07-05 07:55:06,299 - INFO - Dataset Size: 15417 total records
2025-07-05 07:55:06,299 - INFO - Training Size: 10791 records
2025-07-05 07:55:06,299 - INFO - 
Classification Report:
2025-07-05 07:55:06,307 - INFO -               precision    recall  f1-score   support

           0       0.92      0.98      0.95      1157
           1       0.98      0.91      0.95      1156

    accuracy                           0.95      2313
   macro avg       0.95      0.95      0.95      2313
weighted avg       0.95      0.95      0.95      2313

2025-07-05 07:55:06,310 - INFO - 
Confusion Matrix:
2025-07-05 07:55:06,311 - INFO - True\Pred    0    1
2025-07-05 07:55:06,311 - INFO - 0         1135   22
2025-07-05 07:55:06,311 - INFO - 1          100 1056
2025-07-05 07:55:06,311 - INFO - ================================================================================
2025-07-05 07:55:06,312 - INFO - FULL WNBA DATASET TRAINING COMPLETED SUCCESSFULLY!
2025-07-05 07:55:06,312 - INFO - ================================================================================
2025-07-05 07:57:28,264 - INFO - STARTING FULL WNBA DATASET TRAINING
2025-07-05 07:57:28,264 - INFO - Using proven feature audit approach that achieved 90.1% accuracy
2025-07-05 07:57:28,264 - INFO - ================================================================================
2025-07-05 07:57:28,707 - INFO -  NBA API Endpoints loaded successfully (class import fix).
2025-07-05 07:57:28,728 - INFO - LOADING COMPLETE WNBA DATASET
2025-07-05 07:57:28,728 - INFO - Using database source for maximum data consistency
2025-07-05 07:57:28,729 - INFO - --- Initializing HYPER MEDUSA NEURAL VAULT Data Connector ---
2025-07-05 07:57:28,729 - INFO - BasketballDataLoader initialized with NBA API connector.
2025-07-05 07:57:28,904 - INFO -    games (schedule): 135,816 records
2025-07-05 07:57:28,905 - INFO -    player_game_stats (performance): 26,213 records
2025-07-05 07:57:29,055 - INFO - Successfully loaded complete WNBA dataset: (15417, 23)
2025-07-05 07:57:29,055 - INFO - Seasons covered: Unknown
2025-07-05 07:57:29,056 - INFO - Players covered: Unknown
2025-07-05 07:57:29,056 - INFO - APPLYING PROVEN FEATURE AUDIT
2025-07-05 07:57:29,056 - INFO - APPLYING PROVEN FEATURE AUDIT APPROACH
2025-07-05 07:57:29,056 - INFO - ============================================================
2025-07-05 07:57:29,056 - INFO - Original data: (15417, 23)
2025-07-05 07:57:29,062 - INFO - STEP 2: Removing 6 categorical features: ['rank_position', 'engineered_feature_15', 'engineered_feature_16', 'engineered_feature_17', 'engineered_feature_18']...
2025-07-05 07:57:29,065 - INFO - After categorical removal: (15417, 17)
2025-07-05 07:57:29,081 - INFO - STEP 4: Removing 7 low-variance features
2025-07-05 07:57:29,083 - INFO - ============================================================
2025-07-05 07:57:29,084 - INFO - FEATURE AUDIT COMPLETE: (15417, 23) -> (15417, 10)
2025-07-05 07:57:29,084 - INFO - Final features: ['stat_value', 'season_encoded', 'high_performer', 'top_10_rank', 'above_average_performer', 'stat_value_normalized', 'stat_value_log', 'stat_value_squared', 'stat_value_percentile', 'win_prediction']
2025-07-05 07:57:29,084 - INFO - ============================================================
2025-07-05 07:57:29,085 - INFO - PREPARING FEATURES AND TARGET
2025-07-05 07:57:29,091 - INFO - Target quality check: {np.int64(0): np.int64(7710), np.int64(1): np.int64(7707)}
2025-07-05 07:57:29,091 - INFO - Class balance ratio: 0.500
2025-07-05 07:57:29,103 - INFO - Train: 10791 samples, {np.int64(0): np.int64(5397), np.int64(1): np.int64(5394)}
2025-07-05 07:57:29,104 - INFO - Val: 2313 samples, {np.int64(0): np.int64(1156), np.int64(1): np.int64(1157)}
2025-07-05 07:57:29,105 - INFO - Test: 2313 samples, {np.int64(0): np.int64(1157), np.int64(1): np.int64(1156)}
2025-07-05 07:57:29,112 - INFO - CREATING PROVEN MODEL ARCHITECTURE
2025-07-05 07:57:29,117 - INFO - Model: 9 features -> 425 parameters
2025-07-05 07:57:29,118 - INFO - Feature-to-parameter ratio: 1:47
2025-07-05 07:57:30,321 - INFO - STARTING TRAINING ON COMPLETE DATASET
2025-07-05 07:57:30,321 - INFO - ============================================================
2025-07-05 07:57:30,334 - INFO - Epoch  1: Train Loss: 0.9031, Val Loss: 0.7554, Train Acc: 0.476, Val Acc: 0.495
2025-07-05 07:57:30,363 - INFO - Epoch  2: Train Loss: 0.9099, Val Loss: 0.7424, Train Acc: 0.492, Val Acc: 0.495
2025-07-05 07:57:30,390 - INFO - Epoch  3: Train Loss: 0.8702, Val Loss: 0.7280, Train Acc: 0.498, Val Acc: 0.495
2025-07-05 07:57:30,406 - INFO - Epoch  4: Train Loss: 0.8836, Val Loss: 0.7161, Train Acc: 0.497, Val Acc: 0.495
2025-07-05 07:57:30,430 - INFO - Epoch  5: Train Loss: 0.8635, Val Loss: 0.7059, Train Acc: 0.501, Val Acc: 0.495
2025-07-05 07:57:30,457 - INFO - Epoch  6: Train Loss: 0.8499, Val Loss: 0.6887, Train Acc: 0.501, Val Acc: 0.495
2025-07-05 07:57:30,472 - INFO - Epoch  7: Train Loss: 0.8588, Val Loss: 0.6733, Train Acc: 0.506, Val Acc: 0.495
2025-07-05 07:57:30,488 - INFO - Epoch  8: Train Loss: 0.8457, Val Loss: 0.6594, Train Acc: 0.509, Val Acc: 0.876
2025-07-05 07:57:30,504 - INFO - Epoch  9: Train Loss: 0.8397, Val Loss: 0.6472, Train Acc: 0.512, Val Acc: 0.876
2025-07-05 07:57:30,528 - INFO - Epoch 10: Train Loss: 0.8122, Val Loss: 0.6362, Train Acc: 0.517, Val Acc: 0.876
2025-07-05 07:57:30,545 - INFO - Epoch 11: Train Loss: 0.8137, Val Loss: 0.6257, Train Acc: 0.517, Val Acc: 0.876
2025-07-05 07:57:30,565 - INFO - Epoch 12: Train Loss: 0.7975, Val Loss: 0.6161, Train Acc: 0.529, Val Acc: 0.876
2025-07-05 07:57:30,586 - INFO - Epoch 13: Train Loss: 0.7935, Val Loss: 0.6061, Train Acc: 0.532, Val Acc: 0.876
2025-07-05 07:57:30,604 - INFO - Epoch 14: Train Loss: 0.7816, Val Loss: 0.5986, Train Acc: 0.530, Val Acc: 0.876
2025-07-05 07:57:30,620 - INFO - Epoch 15: Train Loss: 0.7846, Val Loss: 0.5915, Train Acc: 0.531, Val Acc: 0.883
2025-07-05 07:57:30,644 - INFO - Epoch 16: Train Loss: 0.7675, Val Loss: 0.5849, Train Acc: 0.540, Val Acc: 0.883
2025-07-05 07:57:30,663 - INFO - Epoch 17: Train Loss: 0.7745, Val Loss: 0.5784, Train Acc: 0.537, Val Acc: 0.895
2025-07-05 07:57:30,683 - INFO - Epoch 18: Train Loss: 0.7581, Val Loss: 0.5721, Train Acc: 0.548, Val Acc: 0.909
2025-07-05 07:57:30,696 - INFO - Epoch 19: Train Loss: 0.7464, Val Loss: 0.5652, Train Acc: 0.544, Val Acc: 0.916
2025-07-05 07:57:30,713 - INFO - Epoch 20: Train Loss: 0.7507, Val Loss: 0.5583, Train Acc: 0.547, Val Acc: 0.921
2025-07-05 07:57:30,733 - INFO - Epoch 21: Train Loss: 0.7273, Val Loss: 0.5522, Train Acc: 0.553, Val Acc: 0.923
2025-07-05 07:57:30,751 - INFO - Epoch 22: Train Loss: 0.7390, Val Loss: 0.5466, Train Acc: 0.558, Val Acc: 0.926
2025-07-05 07:57:30,776 - INFO - Epoch 23: Train Loss: 0.7264, Val Loss: 0.5415, Train Acc: 0.558, Val Acc: 0.927
2025-07-05 07:57:30,796 - INFO - Epoch 24: Train Loss: 0.7198, Val Loss: 0.5365, Train Acc: 0.558, Val Acc: 0.936
2025-07-05 07:57:30,820 - INFO - Epoch 25: Train Loss: 0.7083, Val Loss: 0.5319, Train Acc: 0.571, Val Acc: 0.936
2025-07-05 07:57:30,841 - INFO - Epoch 26: Train Loss: 0.6993, Val Loss: 0.5275, Train Acc: 0.581, Val Acc: 0.936
2025-07-05 07:57:30,866 - INFO - Epoch 27: Train Loss: 0.6989, Val Loss: 0.5231, Train Acc: 0.579, Val Acc: 0.938
2025-07-05 07:57:30,883 - INFO - Epoch 28: Train Loss: 0.6951, Val Loss: 0.5190, Train Acc: 0.573, Val Acc: 0.938
2025-07-05 07:57:30,900 - INFO - Epoch 29: Train Loss: 0.6909, Val Loss: 0.5151, Train Acc: 0.578, Val Acc: 0.935
2025-07-05 07:57:30,929 - INFO - Epoch 30: Train Loss: 0.6866, Val Loss: 0.5118, Train Acc: 0.581, Val Acc: 0.939
2025-07-05 07:57:30,970 - INFO - Epoch 31: Train Loss: 0.6782, Val Loss: 0.5079, Train Acc: 0.585, Val Acc: 0.937
2025-07-05 07:57:31,006 - INFO - Epoch 32: Train Loss: 0.6722, Val Loss: 0.5044, Train Acc: 0.591, Val Acc: 0.937
2025-07-05 07:57:31,030 - INFO - Epoch 33: Train Loss: 0.6684, Val Loss: 0.5009, Train Acc: 0.591, Val Acc: 0.932
2025-07-05 07:57:31,060 - INFO - Epoch 34: Train Loss: 0.6708, Val Loss: 0.4978, Train Acc: 0.595, Val Acc: 0.931
2025-07-05 07:57:31,085 - INFO - Epoch 35: Train Loss: 0.6753, Val Loss: 0.4948, Train Acc: 0.600, Val Acc: 0.933
2025-07-05 07:57:31,111 - INFO - Epoch 36: Train Loss: 0.6654, Val Loss: 0.4920, Train Acc: 0.603, Val Acc: 0.932
2025-07-05 07:57:31,146 - INFO - Epoch 37: Train Loss: 0.6711, Val Loss: 0.4890, Train Acc: 0.592, Val Acc: 0.932
2025-07-05 07:57:31,172 - INFO - Epoch 38: Train Loss: 0.6553, Val Loss: 0.4864, Train Acc: 0.607, Val Acc: 0.942
2025-07-05 07:57:31,214 - INFO - Epoch 39: Train Loss: 0.6650, Val Loss: 0.4835, Train Acc: 0.602, Val Acc: 0.942
2025-07-05 07:57:31,251 - INFO - Epoch 40: Train Loss: 0.6526, Val Loss: 0.4811, Train Acc: 0.600, Val Acc: 0.942
2025-07-05 07:57:31,282 - INFO - Epoch 41: Train Loss: 0.6375, Val Loss: 0.4787, Train Acc: 0.622, Val Acc: 0.942
2025-07-05 07:57:31,312 - INFO - Epoch 42: Train Loss: 0.6356, Val Loss: 0.4765, Train Acc: 0.618, Val Acc: 0.947
2025-07-05 07:57:31,334 - INFO - Epoch 43: Train Loss: 0.6428, Val Loss: 0.4745, Train Acc: 0.619, Val Acc: 0.947
2025-07-05 07:57:31,355 - INFO - Epoch 44: Train Loss: 0.6395, Val Loss: 0.4719, Train Acc: 0.617, Val Acc: 0.947
2025-07-05 07:57:31,375 - INFO - Epoch 45: Train Loss: 0.6347, Val Loss: 0.4700, Train Acc: 0.614, Val Acc: 0.947
2025-07-05 07:57:31,395 - INFO - Epoch 46: Train Loss: 0.6266, Val Loss: 0.4678, Train Acc: 0.620, Val Acc: 0.947
2025-07-05 07:57:31,417 - INFO - Epoch 47: Train Loss: 0.6243, Val Loss: 0.4657, Train Acc: 0.629, Val Acc: 0.947
2025-07-05 07:57:31,431 - INFO - Epoch 48: Train Loss: 0.6239, Val Loss: 0.4640, Train Acc: 0.634, Val Acc: 0.947
2025-07-05 07:57:31,452 - INFO - Epoch 49: Train Loss: 0.6277, Val Loss: 0.4622, Train Acc: 0.630, Val Acc: 0.947
2025-07-05 07:57:31,495 - INFO - Epoch 50: Train Loss: 0.6156, Val Loss: 0.4607, Train Acc: 0.634, Val Acc: 0.947
2025-07-05 07:57:31,499 - INFO - FINAL EVALUATION ON COMPLETE DATASET
2025-07-05 07:57:31,549 - INFO - ================================================================================
2025-07-05 07:57:31,550 - INFO - FULL DATASET TRAINING RESULTS
2025-07-05 07:57:31,550 - INFO - ================================================================================
2025-07-05 07:57:31,550 - INFO - Final Test Accuracy: 0.9477
2025-07-05 07:57:31,550 - INFO - Dataset Size: 15417 total records
2025-07-05 07:57:31,550 - INFO - Training Size: 10791 records
2025-07-05 07:57:31,550 - INFO - 
Classification Report:
2025-07-05 07:57:31,557 - INFO -               precision    recall  f1-score   support

           0       0.91      1.00      0.95      1157
           1       1.00      0.90      0.94      1156

    accuracy                           0.95      2313
   macro avg       0.95      0.95      0.95      2313
weighted avg       0.95      0.95      0.95      2313

2025-07-05 07:57:31,561 - INFO - 
Confusion Matrix:
2025-07-05 07:57:31,561 - INFO - True\Pred    0    1
2025-07-05 07:57:31,561 - INFO - 0         1157    0
2025-07-05 07:57:31,561 - INFO - 1          121 1035
2025-07-05 07:57:31,562 - INFO - ================================================================================
2025-07-05 07:57:31,562 - INFO - FULL WNBA DATASET TRAINING COMPLETED SUCCESSFULLY!
2025-07-05 07:57:31,562 - INFO - ================================================================================
2025-07-05 07:58:53,927 - INFO - STARTING FULL WNBA DATASET TRAINING
2025-07-05 07:58:53,928 - INFO - Using proven feature audit approach that achieved 90.1% accuracy
2025-07-05 07:58:53,928 - INFO - ================================================================================
2025-07-05 07:58:54,380 - INFO -  NBA API Endpoints loaded successfully (class import fix).
2025-07-05 07:58:54,402 - INFO - LOADING COMPLETE WNBA DATASET
2025-07-05 07:58:54,402 - INFO - Using database source for maximum data consistency
2025-07-05 07:58:54,402 - INFO - --- Initializing HYPER MEDUSA NEURAL VAULT Data Connector ---
2025-07-05 07:58:54,403 - INFO - BasketballDataLoader initialized with NBA API connector.
2025-07-05 07:58:54,582 - INFO -    games (schedule): 135,816 records
2025-07-05 07:58:54,583 - INFO -    player_game_stats (performance): 26,213 records
2025-07-05 07:58:54,741 - INFO - Successfully loaded complete WNBA dataset: (15417, 23)
2025-07-05 07:58:54,741 - INFO - Seasons covered: Unknown
2025-07-05 07:58:54,741 - INFO - Players covered: Unknown
2025-07-05 07:58:54,742 - INFO - APPLYING PROVEN FEATURE AUDIT
2025-07-05 07:58:54,742 - INFO - APPLYING PROVEN FEATURE AUDIT APPROACH
2025-07-05 07:58:54,742 - INFO - ============================================================
2025-07-05 07:58:54,742 - INFO - Original data: (15417, 23)
2025-07-05 07:58:54,749 - INFO - STEP 2: Removing 6 categorical features: ['rank_position', 'engineered_feature_15', 'engineered_feature_16', 'engineered_feature_17', 'engineered_feature_18']...
2025-07-05 07:58:54,756 - INFO - After categorical removal: (15417, 17)
2025-07-05 07:58:54,773 - INFO - STEP 4: Removing 7 low-variance features
2025-07-05 07:58:54,774 - INFO - ============================================================
2025-07-05 07:58:54,774 - INFO - FEATURE AUDIT COMPLETE: (15417, 23) -> (15417, 10)
2025-07-05 07:58:54,774 - INFO - Final features: ['stat_value', 'season_encoded', 'high_performer', 'top_10_rank', 'above_average_performer', 'stat_value_normalized', 'stat_value_log', 'stat_value_squared', 'stat_value_percentile', 'win_prediction']
2025-07-05 07:58:54,774 - INFO - ============================================================
2025-07-05 07:58:54,776 - INFO - PREPARING FEATURES AND TARGET
2025-07-05 07:58:54,784 - INFO - Target quality check: {np.int64(0): np.int64(7710), np.int64(1): np.int64(7707)}
2025-07-05 07:58:54,784 - INFO - Class balance ratio: 0.500
2025-07-05 07:58:54,793 - INFO - Train: 10791 samples, {np.int64(0): np.int64(5397), np.int64(1): np.int64(5394)}
2025-07-05 07:58:54,794 - INFO - Val: 2313 samples, {np.int64(0): np.int64(1156), np.int64(1): np.int64(1157)}
2025-07-05 07:58:54,795 - INFO - Test: 2313 samples, {np.int64(0): np.int64(1157), np.int64(1): np.int64(1156)}
2025-07-05 07:58:54,800 - INFO - CREATING PROVEN MODEL ARCHITECTURE
2025-07-05 07:58:54,802 - INFO - Model: 9 features -> 425 parameters
2025-07-05 07:58:54,802 - INFO - Feature-to-parameter ratio: 1:47
2025-07-05 07:58:55,994 - INFO - STARTING TRAINING ON COMPLETE DATASET
2025-07-05 07:58:55,994 - INFO - ============================================================
2025-07-05 07:58:56,006 - INFO - Epoch  1: Train Loss: 1.2380, Val Loss: 0.7800, Train Acc: 0.475, Val Acc: 0.453
2025-07-05 07:58:56,023 - INFO - Epoch  2: Train Loss: 1.2285, Val Loss: 0.7572, Train Acc: 0.461, Val Acc: 0.453
2025-07-05 07:58:56,040 - INFO - Epoch  3: Train Loss: 1.1642, Val Loss: 0.7377, Train Acc: 0.488, Val Acc: 0.453
2025-07-05 07:58:56,057 - INFO - Epoch  4: Train Loss: 1.1629, Val Loss: 0.7192, Train Acc: 0.489, Val Acc: 0.453
2025-07-05 07:58:56,074 - INFO - Epoch  5: Train Loss: 1.1311, Val Loss: 0.7005, Train Acc: 0.484, Val Acc: 0.453
2025-07-05 07:58:56,089 - INFO - Epoch  6: Train Loss: 1.1231, Val Loss: 0.6820, Train Acc: 0.492, Val Acc: 0.459
2025-07-05 07:58:56,133 - INFO - Epoch  7: Train Loss: 1.0735, Val Loss: 0.6643, Train Acc: 0.494, Val Acc: 0.466
2025-07-05 07:58:56,146 - INFO - Epoch  8: Train Loss: 1.0572, Val Loss: 0.6483, Train Acc: 0.499, Val Acc: 0.467
2025-07-05 07:58:56,160 - INFO - Epoch  9: Train Loss: 1.0741, Val Loss: 0.6328, Train Acc: 0.496, Val Acc: 0.466
2025-07-05 07:58:56,174 - INFO - Epoch 10: Train Loss: 1.0679, Val Loss: 0.6177, Train Acc: 0.506, Val Acc: 0.514
2025-07-05 07:58:56,199 - INFO - Epoch 11: Train Loss: 1.0448, Val Loss: 0.6031, Train Acc: 0.508, Val Acc: 0.513
2025-07-05 07:58:56,234 - INFO - Epoch 12: Train Loss: 1.0266, Val Loss: 0.5895, Train Acc: 0.506, Val Acc: 0.519
2025-07-05 07:58:56,250 - INFO - Epoch 13: Train Loss: 1.0051, Val Loss: 0.5768, Train Acc: 0.511, Val Acc: 0.900
2025-07-05 07:58:56,283 - INFO - Epoch 14: Train Loss: 0.9794, Val Loss: 0.5645, Train Acc: 0.511, Val Acc: 0.900
2025-07-05 07:58:56,301 - INFO - Epoch 15: Train Loss: 0.9815, Val Loss: 0.5524, Train Acc: 0.509, Val Acc: 0.901
2025-07-05 07:58:56,328 - INFO - Epoch 16: Train Loss: 0.9694, Val Loss: 0.5404, Train Acc: 0.535, Val Acc: 0.908
2025-07-05 07:58:56,348 - INFO - Epoch 17: Train Loss: 0.9418, Val Loss: 0.5290, Train Acc: 0.525, Val Acc: 0.909
2025-07-05 07:58:56,364 - INFO - Epoch 18: Train Loss: 0.9383, Val Loss: 0.5213, Train Acc: 0.548, Val Acc: 0.909
2025-07-05 07:58:56,380 - INFO - Epoch 19: Train Loss: 0.9081, Val Loss: 0.5137, Train Acc: 0.534, Val Acc: 0.909
2025-07-05 07:58:56,401 - INFO - Epoch 20: Train Loss: 0.9041, Val Loss: 0.5063, Train Acc: 0.544, Val Acc: 0.911
2025-07-05 07:58:56,422 - INFO - Epoch 21: Train Loss: 0.8827, Val Loss: 0.4989, Train Acc: 0.545, Val Acc: 0.913
2025-07-05 07:58:56,438 - INFO - Epoch 22: Train Loss: 0.8754, Val Loss: 0.4915, Train Acc: 0.552, Val Acc: 0.913
2025-07-05 07:58:56,460 - INFO - Epoch 23: Train Loss: 0.8861, Val Loss: 0.4839, Train Acc: 0.552, Val Acc: 0.915
2025-07-05 07:58:56,476 - INFO - Epoch 24: Train Loss: 0.8427, Val Loss: 0.4761, Train Acc: 0.559, Val Acc: 0.917
2025-07-05 07:58:56,496 - INFO - Epoch 25: Train Loss: 0.8337, Val Loss: 0.4694, Train Acc: 0.558, Val Acc: 0.918
2025-07-05 07:58:56,516 - INFO - Epoch 26: Train Loss: 0.8266, Val Loss: 0.4620, Train Acc: 0.568, Val Acc: 0.920
2025-07-05 07:58:56,531 - INFO - Epoch 27: Train Loss: 0.8131, Val Loss: 0.4554, Train Acc: 0.568, Val Acc: 0.920
2025-07-05 07:58:56,546 - INFO - Epoch 28: Train Loss: 0.8282, Val Loss: 0.4490, Train Acc: 0.569, Val Acc: 0.922
2025-07-05 07:58:56,566 - INFO - Epoch 29: Train Loss: 0.7854, Val Loss: 0.4426, Train Acc: 0.572, Val Acc: 0.924
2025-07-05 07:58:56,593 - INFO - Epoch 30: Train Loss: 0.7724, Val Loss: 0.4368, Train Acc: 0.595, Val Acc: 0.926
2025-07-05 07:58:56,630 - INFO - Epoch 31: Train Loss: 0.7594, Val Loss: 0.4311, Train Acc: 0.588, Val Acc: 0.935
2025-07-05 07:58:56,655 - INFO - Epoch 32: Train Loss: 0.7633, Val Loss: 0.4260, Train Acc: 0.590, Val Acc: 0.936
2025-07-05 07:58:56,673 - INFO - Epoch 33: Train Loss: 0.7483, Val Loss: 0.4207, Train Acc: 0.594, Val Acc: 0.937
2025-07-05 07:58:56,684 - INFO - Epoch 34: Train Loss: 0.7310, Val Loss: 0.4158, Train Acc: 0.605, Val Acc: 0.939
2025-07-05 07:58:56,703 - INFO - Epoch 35: Train Loss: 0.7385, Val Loss: 0.4112, Train Acc: 0.607, Val Acc: 0.939
2025-07-05 07:58:56,725 - INFO - Epoch 36: Train Loss: 0.7049, Val Loss: 0.4069, Train Acc: 0.617, Val Acc: 0.941
2025-07-05 07:58:56,742 - INFO - Epoch 37: Train Loss: 0.7021, Val Loss: 0.4029, Train Acc: 0.624, Val Acc: 0.942
2025-07-05 07:58:56,757 - INFO - Epoch 38: Train Loss: 0.6880, Val Loss: 0.3992, Train Acc: 0.614, Val Acc: 0.945
2025-07-05 07:58:56,775 - INFO - Epoch 39: Train Loss: 0.6960, Val Loss: 0.3957, Train Acc: 0.624, Val Acc: 0.946
2025-07-05 07:58:56,792 - INFO - Epoch 40: Train Loss: 0.6776, Val Loss: 0.3923, Train Acc: 0.634, Val Acc: 0.947
2025-07-05 07:58:56,822 - INFO - Epoch 41: Train Loss: 0.6597, Val Loss: 0.3890, Train Acc: 0.627, Val Acc: 0.960
2025-07-05 07:58:56,844 - INFO - Epoch 42: Train Loss: 0.6684, Val Loss: 0.3858, Train Acc: 0.635, Val Acc: 0.977
2025-07-05 07:58:56,864 - INFO - Epoch 43: Train Loss: 0.6669, Val Loss: 0.3825, Train Acc: 0.639, Val Acc: 0.969
2025-07-05 07:58:56,883 - INFO - Epoch 44: Train Loss: 0.6528, Val Loss: 0.3795, Train Acc: 0.647, Val Acc: 0.963
2025-07-05 07:58:56,907 - INFO - Epoch 45: Train Loss: 0.6487, Val Loss: 0.3767, Train Acc: 0.648, Val Acc: 0.955
2025-07-05 07:58:56,925 - INFO - Epoch 46: Train Loss: 0.6458, Val Loss: 0.3739, Train Acc: 0.651, Val Acc: 0.951
2025-07-05 07:58:56,944 - INFO - Epoch 47: Train Loss: 0.6370, Val Loss: 0.3712, Train Acc: 0.649, Val Acc: 0.948
2025-07-05 07:58:56,966 - INFO - Epoch 48: Train Loss: 0.6092, Val Loss: 0.3686, Train Acc: 0.660, Val Acc: 0.948
2025-07-05 07:58:56,989 - INFO - Epoch 49: Train Loss: 0.6284, Val Loss: 0.3661, Train Acc: 0.670, Val Acc: 0.951
2025-07-05 07:58:57,013 - INFO - Epoch 50: Train Loss: 0.6207, Val Loss: 0.3629, Train Acc: 0.674, Val Acc: 0.945
2025-07-05 07:58:57,016 - INFO - FINAL EVALUATION ON COMPLETE DATASET
2025-07-05 07:58:57,037 - INFO - ================================================================================
2025-07-05 07:58:57,038 - INFO - FULL DATASET TRAINING RESULTS
2025-07-05 07:58:57,038 - INFO - ================================================================================
2025-07-05 07:58:57,038 - INFO - Final Test Accuracy: 0.9576
2025-07-05 07:58:57,038 - INFO - Dataset Size: 15417 total records
2025-07-05 07:58:57,039 - INFO - Training Size: 10791 records
2025-07-05 07:58:57,039 - INFO - 
Classification Report:
2025-07-05 07:58:57,046 - INFO -               precision    recall  f1-score   support

           0       0.98      0.93      0.96      1157
           1       0.94      0.98      0.96      1156

    accuracy                           0.96      2313
   macro avg       0.96      0.96      0.96      2313
weighted avg       0.96      0.96      0.96      2313

2025-07-05 07:58:57,047 - INFO - 
Confusion Matrix:
2025-07-05 07:58:57,048 - INFO - True\Pred    0    1
2025-07-05 07:58:57,048 - INFO - 0         1078   79
2025-07-05 07:58:57,048 - INFO - 1           19 1137
2025-07-05 07:58:57,049 - INFO - ================================================================================
2025-07-05 07:58:57,049 - INFO - FULL WNBA DATASET TRAINING COMPLETED SUCCESSFULLY!
2025-07-05 07:58:57,049 - INFO - ================================================================================
2025-07-05 07:59:19,273 - INFO - STARTING FULL WNBA DATASET TRAINING
2025-07-05 07:59:19,273 - INFO - Using proven feature audit approach that achieved 90.1% accuracy
2025-07-05 07:59:19,273 - INFO - ================================================================================
2025-07-05 07:59:19,736 - INFO -  NBA API Endpoints loaded successfully (class import fix).
2025-07-05 07:59:19,756 - INFO - LOADING COMPLETE WNBA DATASET
2025-07-05 07:59:19,756 - INFO - Using database source for maximum data consistency
2025-07-05 07:59:19,757 - INFO - --- Initializing HYPER MEDUSA NEURAL VAULT Data Connector ---
2025-07-05 07:59:19,757 - INFO - BasketballDataLoader initialized with NBA API connector.
2025-07-05 07:59:19,932 - INFO -    games (schedule): 135,816 records
2025-07-05 07:59:19,933 - INFO -    player_game_stats (performance): 26,213 records
2025-07-05 07:59:20,077 - INFO - Successfully loaded complete WNBA dataset: (15417, 23)
2025-07-05 07:59:20,078 - INFO - Seasons covered: Unknown
2025-07-05 07:59:20,078 - INFO - Players covered: Unknown
2025-07-05 07:59:20,078 - INFO - APPLYING PROVEN FEATURE AUDIT
2025-07-05 07:59:20,079 - INFO - APPLYING PROVEN FEATURE AUDIT APPROACH
2025-07-05 07:59:20,080 - INFO - ============================================================
2025-07-05 07:59:20,081 - INFO - Original data: (15417, 23)
2025-07-05 07:59:20,087 - INFO - STEP 2: Removing 6 categorical features: ['rank_position', 'engineered_feature_15', 'engineered_feature_16', 'engineered_feature_17', 'engineered_feature_18']...
2025-07-05 07:59:20,092 - INFO - After categorical removal: (15417, 17)
2025-07-05 07:59:20,102 - INFO - STEP 4: Removing 7 low-variance features
2025-07-05 07:59:20,104 - INFO - ============================================================
2025-07-05 07:59:20,104 - INFO - FEATURE AUDIT COMPLETE: (15417, 23) -> (15417, 10)
2025-07-05 07:59:20,104 - INFO - Final features: ['stat_value', 'season_encoded', 'high_performer', 'top_10_rank', 'above_average_performer', 'stat_value_normalized', 'stat_value_log', 'stat_value_squared', 'stat_value_percentile', 'win_prediction']
2025-07-05 07:59:20,105 - INFO - ============================================================
2025-07-05 07:59:20,105 - INFO - PREPARING FEATURES AND TARGET
2025-07-05 07:59:20,111 - INFO - Target quality check: {np.int64(0): np.int64(7710), np.int64(1): np.int64(7707)}
2025-07-05 07:59:20,112 - INFO - Class balance ratio: 0.500
2025-07-05 07:59:20,118 - INFO - Train: 10791 samples, {np.int64(0): np.int64(5397), np.int64(1): np.int64(5394)}
2025-07-05 07:59:20,118 - INFO - Val: 2313 samples, {np.int64(0): np.int64(1156), np.int64(1): np.int64(1157)}
2025-07-05 07:59:20,118 - INFO - Test: 2313 samples, {np.int64(0): np.int64(1157), np.int64(1): np.int64(1156)}
2025-07-05 07:59:20,122 - INFO - CREATING PROVEN MODEL ARCHITECTURE
2025-07-05 07:59:20,123 - INFO - Model: 9 features -> 425 parameters
2025-07-05 07:59:20,124 - INFO - Feature-to-parameter ratio: 1:47
2025-07-05 07:59:21,762 - INFO - STARTING TRAINING ON COMPLETE DATASET
2025-07-05 07:59:21,762 - INFO - ============================================================
2025-07-05 07:59:21,774 - INFO - Epoch  1: Train Loss: 0.8786, Val Loss: 0.5274, Train Acc: 0.569, Val Acc: 0.919
2025-07-05 07:59:21,786 - INFO - Epoch  2: Train Loss: 0.8688, Val Loss: 0.5297, Train Acc: 0.542, Val Acc: 0.939
2025-07-05 07:59:21,795 - INFO - Epoch  3: Train Loss: 0.8462, Val Loss: 0.5315, Train Acc: 0.551, Val Acc: 0.939
2025-07-05 07:59:21,804 - INFO - Epoch  4: Train Loss: 0.8525, Val Loss: 0.5323, Train Acc: 0.536, Val Acc: 0.939
2025-07-05 07:59:21,813 - INFO - Epoch  5: Train Loss: 0.8358, Val Loss: 0.5316, Train Acc: 0.546, Val Acc: 0.939
2025-07-05 07:59:21,822 - INFO - Epoch  6: Train Loss: 0.8086, Val Loss: 0.5334, Train Acc: 0.554, Val Acc: 0.948
2025-07-05 07:59:21,822 - INFO - Early stopping at epoch 6
2025-07-05 07:59:21,822 - INFO - FINAL EVALUATION ON COMPLETE DATASET
2025-07-05 07:59:21,838 - INFO - ================================================================================
2025-07-05 07:59:21,838 - INFO - FULL DATASET TRAINING RESULTS
2025-07-05 07:59:21,838 - INFO - ================================================================================
2025-07-05 07:59:21,838 - INFO - Final Test Accuracy: 0.9269
2025-07-05 07:59:21,838 - INFO - Dataset Size: 15417 total records
2025-07-05 07:59:21,838 - INFO - Training Size: 10791 records
2025-07-05 07:59:21,839 - INFO - 
Classification Report:
2025-07-05 07:59:21,842 - INFO -               precision    recall  f1-score   support

           0       1.00      0.85      0.92      1157
           1       0.87      1.00      0.93      1156

    accuracy                           0.93      2313
   macro avg       0.94      0.93      0.93      2313
weighted avg       0.94      0.93      0.93      2313

2025-07-05 07:59:21,843 - INFO - 
Confusion Matrix:
2025-07-05 07:59:21,844 - INFO - True\Pred    0    1
2025-07-05 07:59:21,844 - INFO - 0          988  169
2025-07-05 07:59:21,844 - INFO - 1            0 1156
2025-07-05 07:59:21,844 - INFO - ================================================================================
2025-07-05 07:59:21,844 - INFO - FULL WNBA DATASET TRAINING COMPLETED SUCCESSFULLY!
2025-07-05 07:59:21,844 - INFO - ================================================================================
